<%@page import="com.ccidit.platform.sdk.UserVO"%>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<base href="<%=basePath%>">

<title></title>

<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
<!--
	<link rel="stylesheet" type="text/css" href="styles.css">
	-->
<link rel="stylesheet" href="${pageContext.request.contextPath }/js/jquery/UI/UI_CSS/ui.all.css">
<script src="${pageContext.request.contextPath }/js/jquery-1.7.2.min.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/ui.core.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.widget.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.mouse.js"></script>
<script src="${pageContext.request.contextPath }/js/jquery/UI/jquery.ui.sortable.js"></script>
<script src="${pageContext.request.contextPath }/js/platformInfo.js"></script>

<style type="text/css">
body {
    margin: 0;
    padding: 10px;
    font-family: "Microsoft YaHei", Arial, sans-serif;
    background-color: #f5f5f5;
}

.workspace-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.module-box {
    background: white;
    border: 1px solid #ddd;
    border-radius: 0;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    border-radius: 5px;
}

.module-header {
    background-image: url('../images/sy/jie-4.png');
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 115px auto;
    background-position: top left;
    border-top-right-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    margin: 0;
    border-bottom: 1px solid #CCE6FF;
    position: relative;
}

/* .module-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background: linear-gradient(to bottom, #ffffff, #CCE6FF);
} */

.module-title {
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
    letter-spacing: 2.5px;
    color: white;
}

.module-content {
    padding: 15px;
    background: white;
    height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}

.more-link {
    color: #6C6C6C;
    text-decoration: none;
    font-size: 12px;
}

/* .more-link:hover {
    color: #6C6C6C;
    text-decoration: none;
} */

.item-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.item-list li {
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-title {
    color: #333;
    text-decoration: none;
    flex: 1;
    margin-right: 10px;
}

.item-title:hover {
    color: #0066cc;
}

.item-date {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
}

.item-meta {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.schedule-item {
    margin-bottom: 0;
    padding: 10px 0;
    background: white;
    border-radius: 0;
    border-bottom: 1px solid #e0e0e0;
}

.schedule-item:last-child {
    border-bottom: none;
}

.schedule-title {
    font-weight: bold;
    color: #0A4DC3;
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.4;
}

.schedule-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
    line-height: 1.3;
}

.schedule-department {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.no-data {
    text-align: center;
    color: #999;
    padding: 20px;
    font-style: italic;
}

/* 滚动条样式美化 */
.module-content::-webkit-scrollbar {
    width: 6px;
}

.module-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.module-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.module-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // 初始化页面数据
    loadMySchedule();
    loadWorkDynamics();
    loadProcessFlow();
    loadNotifications();
    loadMyTodos();
    loadPublicNotices();
});

function loadMySchedule() {
    // 加载我的日程数据
	$.ajax({
		 url: "contentMgt_getMeetingListJson.action",
	    type: 'GET',
	    dataType: 'json',
	    success: function(response) {
	        // 处理返回的 JSON 数据
            if (response && response.meetingList && response.meetingList.length > 0) {
                var meetingList = response.meetingList;
                var html = '';

                // 遍历并生成日程项
                $.each(meetingList, function(index, meeting) {
                    html += '<div class="schedule-item">' +
                           '<div class="schedule-title">' + meeting.content + '</div>' +
                           '<div class="schedule-meta">地点：<span class="schedule-location">' + meeting.room + '</span></div>' +
                           '<div class="schedule-meta">时间：<span class="schedule-time">' + meeting.startTime + ' 至 ' + meeting.endTime + '</span></div>' +
                           '<div class="schedule-meta">发布部门：<span class="schedule-department">' + meeting.deptName + '</span></div>' +
                           '</div>';
                });

                // 使用ID选择器更新日程内容区域
                $('#rcContent').html(html);
            } else {
                $('#rcContent').html('<div class="no-data">暂无日程安排</div>');
            }
        },
        error: function(xhr, status, error) {
            $('#rcContent').html('<div class="no-data">加载失败</div>');
        }
	});
}

function loadWorkDynamics() {
    // 模拟加载局内工作动态
}

function loadProcessFlow() {
    // 模拟加载过程程流转
}

function loadNotifications() {
    // 加载通知公告
    $.ajax({
		 url: "contentMgt_getMessageListJson.action",
	    type: 'GET',
	    dataType: 'json',
	    success: function(response) {
	    // 处理返回的 JSON 数据
            if (response && response.messageList && response.messageList.length > 0) {
                var messageList = response.messageList;
                var html = '<ul class="item-list">';

                // 遍历并生成通知公告项
                $.each(messageList, function(index, message) {
                    html += '<li>' +
                           '<a href="javascript:showMessage(' + message.id + ');" class="item-title">' + message.title + '</a>' +
                           '<span class="item-date">' + message.time + '</span>' +
                           '</li>';
                });

                html += '</ul>';
                // 使用ID选择器更新通知公告内容区域
                $('#tzggContent').html(html);
            } else {
                $('#tzggContent').html('<div class="no-data">暂无通知公告</div>');
            }
        },
        error: function(xhr, status, error) {
            $('#tzggContent').html('<div class="no-data">加载失败</div>');
        }
	});
}

function loadMyTodos() {
    // 加载我的待办
	$.ajax({
		url: "/workflowEngine/background/contentMgt_getMyToDoListJson.action",
	    type: 'GET',
	    dataType: 'json',
	    success: function(response) {
	    // 处理返回的 JSON 数据
           if (response && response.eventMap && response.eventMap.length > 0) {
                var todoList = response.eventMap;
                var html = '<ul class="item-list">';

                // 遍历并生成待办项
                $.each(todoList, function(index, todo) {
                	var anchorHTML = '<a href="javascript:showDetail('
                        + "'" + todo.bizType + "',"
                        + "'" + todo.bizGuid + "',"
                        + todo.formVersionId + ','
                        + todo.tempNodeId + ','
                        + "'" + todo.attach + "',"
                        + todo.state + ');"  title='
                        + todo.bizName + ' class="item-title">【'
                        + todo.cname + '】'
                        + todo.bizName + '</a>';
                    html += '<li>' +
                    	anchorHTML +
                           '<span class="item-date">' + todo.createTime + '</span>' +
                           '</li>';
                });

                html += '</ul>';
               // 使用ID选择器更新待办内容区域
               $('#myToDo').html(html);
           } else {
               $('#myToDo').html('<div class="no-data">暂无业务待办</div>');
           }
       },
       error: function(xhr, status, error) {
           $('#myToDo').html('<div class="no-data">加载失败</div>');
       }
	});
}

function loadPublicNotices() {
    // 模拟加载公示栏
}

	function toMeeting(){
		var YEAR = $("#TYEAR").val();
		var MONTH = $("#TMONTH").val();
		var DAY = $("#TDAY").val();
		//var url = "/meetingAction_list.action?ownerType=department";
		var url = "/meetingAction_Alllist.action?ownerType=department"+'&YEAR='
			+ YEAR+'&MONTH='+MONTH+'&DAY='+DAY;
		window.parent.document.getElementById("mainframe").src = url;
	}

    function showMessage(id) {
		var url = "<s:url action='MessageAction_viewMessage.action' namespace='/' />?id="+ id;
		window.location.href = url;
	}

    function showDetail(bizType, bizGuid, versionId, wfActivityId, attach,state) {

		if((bizType==8001 || bizType==8003) && state!=3){
    		var checkUrl = "/budgetMgtAction_checkBudgetById.action?billid="+bizGuid;
        	$.ajax({
    			type: "POST",
    			url:checkUrl,
    			error: function(request) {
    				alert("验证预算余额失败，请联系系统管理员");

    			},
    			success: function(data) {


    					var url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
    						+ bizType
    						+ "&bizGuid="
    						+ bizGuid
    						+ "&versionId="
    						+ versionId
    						+ "&wfActivityId=" + wfActivityId + "&attach=1";

    				if(bizType == 8006){
    					url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
    						+ bizType
    						+ "&bizGuid="
    						+ bizGuid
    						+ "&versionId="
    						+ versionId
    						+ "&wfActivityId=" + wfActivityId ;

    				}

    				if ((bizType >= 1101 && bizType <= 1117) || bizType == 1119)
    					url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance_GW" />?bizType='
    							+ bizType
    							+ "&bizGuid="
    							+ bizGuid
    							+ "&versionId="
    							+ versionId + "&wfActivityId=" + wfActivityId;

    				if (bizType == 8001 || bizType == 8002 || bizType == 8003) {
    					url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
    							+ bizType
    							+ "&bizGuid="
    							+ bizGuid
    							+ "&versionId="
    							+ versionId + "&wfActivityId=" + wfActivityId+"&state="+state;

    				}

    				//var urlStr="/workflowEngine/templeMgtAction_" + actionName + ".action?bizType="+bizType+"&bizGuid="+bizGuid+"&versionId="+versionId+"&wfActivityId="+wfActivityId+attachstr+"&attach=1";
    				var preurl = "&preurl=/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=27-&funcCode=27-&module=oaMgt";
    				url = url + preurl;
    				window.location.href = url;
    			}

    		});
    	}else{


    		var url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
				+ bizType
				+ "&bizGuid="
				+ bizGuid
				+ "&versionId="
				+ versionId
				+ "&wfActivityId=" + wfActivityId + "&attach=1";

		if(bizType == 8006){
			url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
				+ bizType
				+ "&bizGuid="
				+ bizGuid
				+ "&versionId="
				+ versionId
				+ "&wfActivityId=" + wfActivityId ;

		}

		if ((bizType >= 1101 && bizType <= 1117) || bizType == 1119)
			url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance_GW" />?bizType='
					+ bizType
					+ "&bizGuid="
					+ bizGuid
					+ "&versionId="
					+ versionId + "&wfActivityId=" + wfActivityId;

		if (bizType == 8001 || bizType == 8002 || bizType == 8003) {
			url = '<s:url action="workflowEngine/templeMgtAction_approveWFInstance" />?bizType='
					+ bizType
					+ "&bizGuid="
					+ bizGuid
					+ "&versionId="
					+ versionId + "&wfActivityId=" + wfActivityId+"&state="+state;

		}

		var preurl = "&preurl=/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=27-&funcCode=27-&module=oaMgt";
    	url = url + preurl;
    	window.location.href = url;
    	}

	}
</script>
</head>

<body>
<input type="hidden" class="cxbg_text" id="TYEAR" value="${YEAR}" />
		<input type="hidden" class="cxbg_text" id="TMONTH" value="${MONTH}" />
		<input type="hidden" class="cxbg_text" id="TDAY" value="${DAY}" />
<div class="workspace-container">
    <!-- 我的日程 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">我的日程</span>
            <a href="javascript:toMeeting();" class="more-link" >更多</a>
        </div>
        <div class="module-content" id="rcContent">
              <!-- 日程内容将通过JavaScript动态加载 -->
                 <div class="no-data">加载中...</div>
        </div>
    </div>

    <!-- 局内工作动态 -->
    <div class="module-box">
        <div class="module-header" style="background-size: 145px auto;">
            <span class="module-title" >局内工作动态</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">关于与中经宏观（北京）文化传媒有限公司签署合作协议的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于与国际大学创新联盟、广州澳众智能科技有限公司签订合作协议的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
                <li>
                    <a href="#" class="item-title">关于签署《商务部投资促进事务局数字经济创新基础中心部分中心合作协议》的通知</a>
                </li>
            </ul>
        </div>
    </div>


        <!-- 我的待办 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">我的待办</span>
            <a href="/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=27-&funcCode=27-&module=oaMgt" class="more-link">更多</a>
        </div>
        <div class="module-content" id='myToDo'>
        	<div class="no-data">加载中...</div>
        </div>
    </div>

    <!-- 通知公告 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">通知公告</span>
            <a href="/MessageAction_messageList.action" class="more-link">更多</a>
        </div>
        <div class="module-content" id="tzggContent">
            <div class="no-data">加载中...</div>
        </div>
    </div>

    <!-- 我的提醒 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title">我的提醒</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <div class="no-data">暂无数据</div>
        </div>
    </div>

    <!-- 公示栏 -->
    <div class="module-box">
        <div class="module-header">
            <span class="module-title" style="margin-left:20px;">公示栏</span>
            <a href="#" class="more-link">更多</a>
        </div>
        <div class="module-content">
            <ul class="item-list">
                <li>
                    <a href="#" class="item-title">关于举办商务部干部职工羽毛球赛的通知</a>
                    <span class="item-date">2025-07-08</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于协和体检的补充通知</a>
                    <span class="item-date">2025-07-07</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于在工作平台中新增过程流转功能的通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">临时断网通知</a>
                    <span class="item-date">2025-07-04</span>
                </li>
                <li>
                    <a href="#" class="item-title">关于发布2025年绩效考核任务的通知</a>
                    <span class="item-date">2025-07-03</span>
                </li>
            </ul>
        </div>
    </div>
</div>
</body>
</html>
