package com.ccidit.framework.core.taglib;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;

import com.ccidit.core.cas.CCIDCAS;
import com.ccidit.core.util.UserSessionUtil;
import com.ccidit.framework.core.UserContext;
import com.ccidit.platform.sdk.Client;
import com.ccidit.platform.sdk.ResourceVO;
import com.ccidit.platform.sdk.UserVO;

import com.opensymphony.xwork2.util.ValueStack;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspWriter;

import org.apache.struts2.ServletActionContext;
import org.apache.struts2.components.Component;
import org.apache.struts2.views.jsp.ComponentTagSupport;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;


public class BizBannerTag extends ComponentTagSupport {
	private String funcCode;
	private String count;
   

public String getFuncCode() {
		return funcCode;
	}

	public void setFuncCode(String funcCode) {
		this.funcCode = funcCode;
	}
	
	

public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

public int doStartTag() throws JspException {
		JspWriter writer = this.pageContext.getOut();
		StringBuffer buffer = new StringBuffer();
		String token=CCIDCAS.readToken(ServletActionContext.getRequest());
//		String currentServerNodeUrl=Client.getServernodeUrlById(Client.getServerNodeId());
		String currentServerNodeUrl= CCIDCAS.getFullUrl(ServletActionContext.getRequest(), 2);
		 String homeURL= CCIDCAS.getFullUrl(ServletActionContext.getRequest(), 2);
		 homeURL = "https://***********:8443/frameworkMgt_sysIndex.action";
//		currentServerNodeUrl = currentServerNodeUrl + "/logout";
//		currentServerNodeUrl = currentServerNodeUrl.substring(23);
		 UserVO user=UserContext.getCurrentContext().getCurrentUser();
		buffer.append("<div class=\"header\"> ");
		//buffer.append("<div class=\"topnav\">" +
		buffer.append("<div class=\"topnav\">");
		//		" <a href=\"javascript:;\" onclick=\"openSpaceIndex('/contentMgt_personalSpaceIndex.action','1');\" id='spaceIndex_1' class=\"personal red_color\" >个人空间</a>&nbsp;" +
		//		" <a href=\"javascript:;\" onclick=\"openSpaceIndex('/contentMgt_deptSpaceIndex.action','2')\" id='spaceIndex_2' class=\"department\">部门空间</a>&nbsp; " +
		//		"<a href=\"javascript:;\" onclick=\"openSpaceIndex('/contentMgt_compSpaceIndex.action','3')\" id='spaceIndex_3' class=\"company\">公司空间</a>&nbsp;" +
		//		" <a href=\"javascript:;\" onclick=\"openSpaceIndex('/contentMgt_groupSpaceIndex.action','4')\" id='spaceIndex_4' class=\"group\">集团空间</a>&nbsp; ");
		//buffer.append("<a href=\"\" class=\"help\">帮助</a>");
		//if(currentServerNodeUrl!=null&&!"".equals(currentServerNodeUrl.trim())){
		//	currentServerNodeUrl+="/logout";
			//buffer.append("&nbsp;<a href=\"https://biz.cipa.org.cn\" class=\"refresh\">刷新</a>&nbsp;<a href=\"").append(currentServerNodeUrl).append("\" class=\"exit\">退出</a> </div>");
			buffer.append("</div>");
		//}else{
	     	//buffer.append("&nbsp; <a href=\"https://biz.cipa.org.cn/ssouser/logout\" class=\"exit\">退出</a> </div>");
			//buffer.append("&nbsp;<a href=\"https://biz.cipa.org.cn\" class=\"refresh\">刷新</a>&nbsp;<a href=\"https://biz.cipa.org.cn/logout\" class=\"exit\">退出</a> </div>");
		//	buffer.append("</div>");
		//}
		buffer.append("<div class=\"red_mainnav\">");
		//if("135".equals(user.getId().toString())||"1244".equals(user.getId().toString())||"1319".equals(user.getId().toString())){//管理员
		if("135".equals(user.getId().toString())){//
		//if("135".equals(user.getId().toString())||"1244".equals(user.getId().toString())||"1281".equals(user.getId().toString())||"1319".equals(user.getId().toString())||"1363".equals(user.getId().toString())){//管理员
			buffer.append("<ul>");

			
			
			
			   /* buffer.append("<li  class=\"select\" style=\"cursor: pointer;\"><a  href=\""); 
			     
			      buffer.append(homeURL);
			      buffer.append("\">");
			      buffer.append("首页</a></li>");
					buffer.append("<li class=\"select2\" style=\"cursor:pointer;\"><a href=\"http://************:7001/cipa?token=\" "+token);
					buffer.append("\">工作岗位</a></li>");*/
			    funcCode="none";	
				String menu=drawBannerMenu(funcCode);
				buffer.append(menu);
				/*buffer.append("<li  class=\"select1\"  style=\"cursor: pointer;\">"); 

			    buffer.append("业务事项</li>");*/

				
				
				
				buffer.append("</ul>");
		}
		buffer.append("</div>");
		buffer.append("</div>");
		
		try {
			writer.print(buffer.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return 1;
	}

	public int doEndTag() throws JspException {
		JspWriter writer = this.pageContext.getOut();
		StringBuffer buffer = new StringBuffer();
		buffer.append("<div class=\"nav_line\"></div>");
		buffer.append("<div class=\"notice_mes0\">");
		buffer.append("<div class=\"notice_mes1\">");
		

		buffer.append("</div>");
		
/*		buffer.append("<div class=\"notice_mes2\">");
		buffer.append("<ul>");
		buffer.append("<font style=&quot;font-weight:bolder;font-size:12;&quot; color='black'><li onclick=\"openSelf('https://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=37-&funcCode=37-&module=','37-')\" style=\"cursor: pointer;\"><a  href=\"#\" >综合事务</a></li></font>");
		
		buffer.append("<font style=&quot;font-weight:bolder;font-size:12;&quot; color='black'><li onclick=\"openSelf('https://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=43-&funcCode=43-&module=','43-')\" style=\"cursor: pointer;\"><a  href=\"#\" >渠道建设</a></li></font>");
		buffer.append("<font style=&quot;font-weight:bolder;font-size:12;&quot; color='black'><li onclick=\"openSelf('https://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=44-&funcCode=44-&module=','44-')\" style=\"cursor: pointer;\"><a  href=\"#\" >产业资源建设</a></li></font>");
		buffer.append("<font style=&quot;font-weight:bolder;font-size:12;&quot; color='black'><li onclick=\"openSelf('https://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=23-&funcCode=23-&module=','23-')\" style=\"cursor: pointer;\"><a  href=\"#\" >风险控制</a></li></font>");
		buffer.append("<font style=&quot;font-weight:bolder;font-size:12;&quot; color='black'><li onclick=\"openSelf('https://biz.cipa.org.cn/workflowEngine/background/templeMgtAction_homeList.action?bizString=-1&funcCode=39-&funcCode=39-&module=','39-')\" style=\"cursor: pointer;\"><a  href=\"#\" >信息网络</a></li></font>");
		
		
		buffer.append("</ul>");
		buffer.append("</div>");
		buffer.append("<div class=\"notice_mes3\">");
		buffer.append("</div>");*/
		
		buffer.append("</div>");

		
		buffer.append("<div class=\"notice_mes\">");
		//AttributePrincipal principal = (AttributePrincipal) ServletActionContext.getRequest().getUserPrincipal();
		//String username = principal.getName();
		
		//String username = CCIDCAS.readUserInfo(ServletActionContext.getRequest());
		
		//单点登陆：
		/*final Assertion assertion = (Assertion) ServletActionContext.getRequest().getSession()
				.getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
		String alias = assertion.getPrincipal().getName();
		String username = Client.getUserCnameByAlias(alias);
		UserVO user=Client.getLoginUserByName(username);*/
		
		String alias = CCIDCAS.readUserInfo(ServletActionContext.getRequest());
		String token=CCIDCAS.readToken(ServletActionContext.getRequest());
		String username = Client.getUserCnameByAlias(alias);
		UserVO user=Client.getLoginUserByName(username);
		 String homeURL= CCIDCAS.getFullUrl(ServletActionContext.getRequest(), 2);
		 //homeURL = "https://***********:8443/frameworkMgt_sysIndex.action";
		 homeURL = "/contentMgt_personalSpaceIndex.action";
		 
		
		buffer.append("<div class=\"notice\"><font class=\"noticeFont\">");
		
		buffer.append(user.getAlias());
		
		String deptName = CCIDCAS.getDeptName(user.getDeptId());
		if(deptName!=null && !deptName.isEmpty()){
			buffer.append("（"+deptName+"）");
		}
		
		buffer.append(","+getwelcom()+"!"+"</font>");
		buffer.append(" <a style=\"margin-left:50px\" href=\" ");
		 buffer.append(homeURL);
		 buffer.append("\"> <font class=\"noticeFont\">首页</font></a>");
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR); 
		int month = cal.get(Calendar.MONTH )+1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		//buffer.append(" 今天是"+year+"年"+month+"月"+day+"日 "+getWeekDay()+"！</div>");
/*		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>保密提醒：平台严禁发布、处理涉密信息！</font>");
		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
				" &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
				"<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>综合事务</font>");
		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>渠道建设</font>");
		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>产业资源建设</font>");
		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>风险控制</font>");
		buffer.append("&nbsp;&nbsp;&nbsp;&nbsp;<font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>信息网络</font>");*/
		buffer.append("</div>");
		//buffer.append("<div class=\"search\">&nbsp;<a href=\"https://biz.cipa.org.cn/frameworkMgt_logout.action\" class=\"exit\"><font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>退出并关闭</font></a>&nbsp;</div>"); 
		//buffer.append("<div class=\"search\">&nbsp;<a href=\"http://***********\" class=\"company\"><font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>返回工作平台</font></a>&nbsp;</div>"); 
		buffer.append("<div class=\"search\">&nbsp;<a href=\"/logout\"><font class=\"noticeFont\">退出</font></a>&nbsp;</div>");
		buffer.append("<div class=\"search\">&nbsp;<a href=\"#\" onclick=\"editPassword()\"><font class=\"noticeFont\">修改密码</font></a>&nbsp;</div>");
		//buffer.append("<div class=\"search\"><a href=\"#\" id='gantanhao' onclick=\"toGroupchat()\"><font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>工作微博</font><span id='chattx' style='color:red'></span></a>&nbsp;&nbsp;</div>"); 
		if(user.getId()!=1201){
			buffer.append("<div class=\"search\" style=\"margin-right:-20px\">&nbsp;<a href=\"#\"  onclick=\"togongshi()\"><font class=\"noticeFont\">公示栏&nbsp;&nbsp;</font></a>&nbsp;&nbsp;</div>");
		}
		 
		//buffer.append("<div class=\"search\">&nbsp;<a href=\"#\"  onclick=\"togongxiang()\"><font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>工作通讯</font></a>&nbsp;</div>");
		if(user.getId()!=1517){
			buffer.append("<div class=\"search\">&nbsp;<a href=\"#\"  onclick=\"toxiaoxi()\"><font class=\"noticeFont\">消息</font></a><span id='xxCount'  style='color:red;position: absolute;right: 10px;bottom: 5px;font-size: 8px;'></span>&nbsp;</div>");
		}
/*		if(UserSessionUtil.isAdmin()){
			
			buffer.append("<div class=\"search\">&nbsp;<a href=\"https://biz.cipa.org.cn/ssouser\" class=\"company\"><font style=&quot;font-weight:bolder;font-size:12;&quot; color='white'>后台</font></a>&nbsp;</div>"); 
		}*/
		buffer.append("</div>");

		try {
			writer.print(buffer.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return 6;
	}

	public Component getBean(ValueStack stack, HttpServletRequest req,
			HttpServletResponse res) {
		return null;
	}
	  
	  private   String drawBannerMenu(String currentFuncCode) {
	      StringBuffer sb = new StringBuffer();
	      List<ResourceVO> list= Client.getFirstLevelResource();
	      for(int i=0;i<list.size();i++){
	    	  ResourceVO resourceVO=list.get(i);
	    	  String  code=resourceVO.getCode();
	    	  String  count=resourceVO.getCode();
	    	  String res=resourceVO.getResString();
	    	  String aa=resourceVO.getName();
/*	    	  if(aa.equals("事项审核")||aa.equals("行政人事党务")||aa.equals("财务事项")||aa.equals("信息网络")){
	    		  continue;
	    	  }*/
/*	    	  if(aa.length()>=0){
	    		  continue;
	    	  }*/
	    	  if(!aa.equals("内部建设")){
	    		 continue;
	    	  }
	    	  StringBuffer hrefsb = new StringBuffer();
	    	  hrefsb.append("openSelf('");
	    	  if(res.indexOf("[ClientServer]")>=0){
	    		  int index=res.indexOf("[ClientServer]")+15;
	    		  res=res.substring(index);
	    	  }
	    	 /* if(res.indexOf("https://biz.cipa.org.cn")>-1){
	    		  int sublength = "https://biz.cipa.org.cn".length();
	    		  res=res.substring(sublength);
	    		  res = CCIDCAS.getFullUrl(ServletActionContext.getRequest(), 2)+res;
	    	  }*/
	    	  hrefsb.append(res);
	    	  if (resourceVO.getResString().indexOf("?") >= 0)
	    		  hrefsb.append("&funcCode=");
              else
            	  hrefsb.append("?funcCode=");
	    	  hrefsb.append(resourceVO.getCode());
	    	  hrefsb.append("&module=" + resourceVO.getModuleCode());
	    	  hrefsb.append("','"+resourceVO.getCode()+"')");
	    	  
	    	  if(code.trim().equals(currentFuncCode)){
	             	sb.append("<li  class=\"select\" style=\"cursor: pointer;\"  onclick=\""+hrefsb+"\" ><a href=\"#\" ");
	          }else{
	        	  sb.append("<li onclick=\""+hrefsb+"\" style=\"cursor: pointer;\"><a  href=\"#\" ");
	          }
	    	 
	    	  sb.append(">");
              sb.append(resourceVO.getName());
              sb.append("</a></li>");
              if("0".equals(count)){
              sb.append("<script>setMainFrame('taskMgt_arrangementTaskList.action?funcCode=27-9-2-')</script>");
              }
             if(code.trim().equals(currentFuncCode)){
	             	sb.append("<input type=\"hidden\" id=\"funcCode\" value=\""+code.trim()+"\">");
	           }
	      }
	      return sb.toString();
	  }
	  
	  private String getwelcom(){
		  Date date =new Date();
		  int hour=date.getHours();
			String message="";
			if(hour>=4&&hour<8){
				message="早上好";
			}
			else if(hour>=8&&hour<11){
				message="上午好";
			}
			else if(hour>=11&&hour<13){
				message="中午好";
			}
			else if(hour>=13&&hour<17){
				message="下午好";
			}
			else if(hour>=17&&hour<23){
				message="晚上好";
			}else{
				message="凌晨好";
			}
		  
		  return message;
	  }
     private String getWeekDay(){
    	 Calendar date = Calendar.getInstance();
    	 date.setTime(new Date());
    	 int i = date.get(Calendar.DAY_OF_WEEK)-1; 
    	 switch(i){ 
    	 case 1:return "星期一"; 
    	 case 2:return "星期二"; 
    	 case 3:return "星期三"; 
    	 case 4:return "星期四"; 
    	 case 5:return "星期五"; 
    	 case 6:return "星期六"; 
    	 case 0:return "星期日"; 
    	 } 
    	 return null;
     }
}
